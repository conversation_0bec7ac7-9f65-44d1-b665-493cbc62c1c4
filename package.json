{"name": "movie-scout-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.0", "axios": "^1.6.7", "@tanstack/react-query": "^5.17.11", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.4.1"}}