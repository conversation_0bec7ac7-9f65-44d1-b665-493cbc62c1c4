import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useWatchlist } from "../contexts/WatchlistContext";
import { StarIcon, TvIcon } from "@heroicons/react/24/outline";
import WatchButton from "../components/WatchButton";

function TVDetails() {
  const { id } = useParams();
  const { addToWatchlist, removeFromWatchlist, watchlist } = useWatchlist();
  const isInWatchlist = watchlist.some((item) => item.id === Number(id));

  const fetchTVDetails = async () => {
    const [tv, credits, videos] = await Promise.all([
      axios.get(
        `https://api.themoviedb.org/3/tv/${id}?api_key=${import.meta.env.VITE_TMDB_API_KEY}&language=en-US`
      ),
      axios.get(
        `https://api.themoviedb.org/3/tv/${id}/credits?api_key=${import.meta.env.VITE_TMDB_API_KEY}`
      ),
      axios.get(
        `https://api.themoviedb.org/3/tv/${id}/videos?api_key=${import.meta.env.VITE_TMDB_API_KEY}&language=en-US`
      ),
    ]);

    return {
      ...tv.data,
      credits: credits.data,
      videos: videos.data,
    };
  };

  const { data: tvShow, isLoading } = useQuery(["tv", id], fetchTVDetails);

  const handleWatchlistToggle = () => {
    if (isInWatchlist) {
      removeFromWatchlist(Number(id));
    } else {
      addToWatchlist({
        id: Number(id),
        title: tvShow.name,
        poster_path: tvShow.poster_path,
        release_date: tvShow.first_air_date,
        vote_average: tvShow.vote_average,
        media_type: "tv",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white"></div>
      </div>
    );
  }

  if (!tvShow) {
    return (
      <div className="text-center text-gray-600 dark:text-gray-400">
        TV show not found
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="relative h-96">
        <img
          src={`https://image.tmdb.org/t/p/original${tvShow.backdrop_path}`}
          alt={tvShow.name}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-8">
          <div className="flex items-center mb-2">
            <TvIcon className="h-6 w-6 text-white mr-2" />
            <h1 className="text-4xl font-bold text-white">{tvShow.name}</h1>
          </div>
          <div className="flex items-center space-x-4 text-white">
            <div className="flex items-center">
              <StarIcon className="h-5 w-5 text-yellow-400" />
              <span className="ml-1">{tvShow.vote_average.toFixed(1)}</span>
            </div>
            <span className="text-gray-300">•</span>
            <span className="text-gray-300">
              {tvShow.number_of_seasons} Season
              {tvShow.number_of_seasons !== 1 ? "s" : ""}
            </span>
            <span className="text-gray-300">•</span>
            <span className="text-gray-300">
              {new Date(tvShow.first_air_date).getFullYear()}
            </span>
            {tvShow.status === "Ended" && tvShow.last_air_date && (
              <>
                <span className="text-gray-300">-</span>
                <span className="text-gray-300">
                  {new Date(tvShow.last_air_date).getFullYear()}
                </span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-1">
            <img
              src={`https://image.tmdb.org/t/p/w500${tvShow.poster_path}`}
              alt={tvShow.name}
              className="w-full h-auto rounded-lg"
            />
          </div>

          <div className="md:col-span-2 space-y-6">
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Overview
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {tvShow.overview}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Status
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {tvShow.status}
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Network
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {tvShow.networks?.map((network) => network.name).join(", ") ||
                    "N/A"}
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Episodes
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {tvShow.number_of_episodes}
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Runtime
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {tvShow.episode_run_time?.length > 0
                    ? `${tvShow.episode_run_time[0]} min`
                    : "N/A"}
                </p>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Genres
              </h3>
              <div className="flex flex-wrap gap-2">
                {tvShow.genres?.map((genre) => (
                  <span
                    key={genre.id}
                    className="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm"
                  >
                    {genre.name}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {tvShow.credits?.cast && tvShow.credits.cast.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Cast
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {tvShow.credits.cast.slice(0, 12).map((actor) => (
                <div key={actor.id} className="text-center">
                  <img
                    src={
                      actor.profile_path
                        ? `https://image.tmdb.org/t/p/w185${actor.profile_path}`
                        : "/placeholder-actor.png"
                    }
                    alt={actor.name}
                    className="w-full h-32 object-cover rounded-lg mb-2"
                  />
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {actor.name}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {actor.character}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-between items-center">
          <button
            onClick={handleWatchlistToggle}
            className="flex items-center px-4 py-2 rounded-md text-sm font-medium bg-primary-500 text-white hover:bg-primary-600"
          >
            {isInWatchlist ? "Remove from Watchlist" : "Add to Watchlist"}
          </button>
          <WatchButton
            videos={tvShow.videos?.results || []}
            imdbId={tvShow.external_ids?.imdb_id}
            title={tvShow.name}
            mediaType="tv"
            size="large"
          />
        </div>
      </div>
    </div>
  );
}

export default TVDetails;
